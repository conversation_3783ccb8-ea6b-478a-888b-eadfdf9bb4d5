
blacklist-remote-write:
  regex:
    - active_cloud_connectors
    - active_cloud_outposts
    - agent_to_group_calculation_count
    - agent_to_group_calculation_duration
    - analytics_llm_requests_bucket
    - analytics_profile_engine_api_cache_hits_created
    - analytics_profile_engine_api_missing_values_created
    - analytics_profile_engine_api_num_of_failures_created
    - analytics_profile_engine_api_role_cache_hits_created
    - analytics_profile_engine_api_role_num_of_failures_created
    - analytics_profile_engine_api_role_total_executions_created
    - analytics_profile_engine_api_total_executions_created
    - analytics__count_events_that_were_fetched_from_bq_total
    - analytics__count_events_that_were_fetched_from_gcs_total
    - analytics_content_loader_data_loader_provider_data_updater_last_update_time
    - analytics_content_loader_data_loader_provider_last_update_time
    - analytics_content_loader_data_updater_failed_update_provider
    - analytics_content_loader_data_updater_fatal_error
    - analytics_content_loader_data_updater_provider_failed_update_entity
    - analytics_content_loader_data_updater_provider_last_update_time
    - analytics_content_loader_data_updater_provider_total_time
    - analytics_de_v2_pubsub_events_uploaded_to_gcs_events_count_total
    - analytics_de_v2_pubsub_events_uploaded_to_gcs_files_count_total
    - analytics_de_v2_vectorized_matcher_detector_count_per_layer_total
    - analytics_de_v2_vectorized_matcher_export_detectors_start_unix_time
    - analytics_de_v2_vectorized_matcher_export_detectors_success_unix_time
    - analytics_de_v2_vectorized_matcher_profiles_api_cache_successful_load_total
    - analytics_de_v2_vectorized_matcher_udf_process_time_count
    - analytics_de_v2_vectorized_matcher_udf_process_time_sum
    - analytics_product_consecutive_stage_failures
    - apisec_asset_manager_assets_retention_flow_duration_seconds_bucket
    - apisec_asset_manager_assets_retention_flow_duration_seconds_count
    - apisec_asset_manager_assets_retention_flow_duration_seconds_sum
    - apisec_asset_manager_get_assets_status_code
    - apisec_asset_manager_publish_uai_flow_duration_seconds_bucket
    - apisec_asset_manager_publish_uai_flow_duration_seconds_count
    - apisec_asset_manager_publish_uai_flow_duration_seconds_sum
    - apisec_asset_manager_pubsub_input_total_errors
    - apisec_asset_manager_total_assets_ETL_timeouts
    - apisec_asset_manager_delete_asset_messages_total
    - apisec_asset_manager_total_number_of_pubsub_input_messages
    - apisec_asset_manager_total_publish_asset_messages
    - apisec_asset_manager_total_UAI_asset_ingestion_errors
    - apisec_asset_manager_upsert_asset_flow_duration_seconds_bucket
    - apisec_asset_manager_upsert_asset_flow_duration_seconds_count
    - apisec_asset_manager_upsert_asset_flow_duration_seconds_sum
    - apisec_bff_api_latency_seconds_bucket
    - apisec_bff_api_request_size_bytes_bucket
    - apisec_bff_api_response_size_bytes_bucket
    - apisec_enricher_apigw_handle_metablob_time_ms
    - apisec_enricher_apigw_http_transactions_creation_error_count
    - apisec_enricher_classification_time_seconds_bucket
    - apisec_enricher_find_geo_ip_errors_count
    - apisec_enricher_find_geo_ip_location_time_ms
    - apisec_enricher_http_handler_handle_metablob_time_ms
    - apisec_enricher_metablob_handler_not_found_count
    - apisec_enricher_metablob_parsing_errors_count
    - apisec_enricher_classification_error_total
    - apisec_enricher_classification_total
    - apisec_grouping_service_api_tree_check_tree_logic_duration_bucket
    - apisec_grouping_service_api_tree_check_tree_logic_duration_count
    - apisec_grouping_service_api_tree_check_tree_logic_duration_sum
    - apisec_grouping_service_mongo_operation_latency_seconds_bucket
    - apisec_grouping_service_mongo_operation_latency_seconds_count
    - apisec_grouping_service_mongo_operation_latency_seconds_sum
    - apisec_grouping_service_pubsub_input_counter
    - apisec_grouping_service_pubsub_input_error_counter
    - apisec_grouping_service_pubsub_output_counter
    - apisec_grouping_service_pubsub_output_error_counter
    - apisec_grouping_service_regex_check_regex_duration_bucket
    - apisec_grouping_service_regex_check_regex_duration_count
    - apisec_grouping_service_regex_check_regex_duration_sum
    - apisec_grouping_service_single_transaction_flow_duration_bucket
    - apisec_grouping_service_single_transaction_flow_duration_count
    - apisec_grouping_service_single_transaction_flow_duration_sum
    - apisec_inspection_api_aggregation_duration_bucket
    - apisec_inspection_api_aggregation_duration_count
    - apisec_inspection_api_aggregation_duration_sum
    - apisec_inspection_content_pull_duration_bucket
    - apisec_inspection_content_update_check_duration_bucket
    - apisec_inspection_content_update_check_duration_count
    - apisec_inspection_content_update_check_duration_sum
    - apisec_inspection_content_update_check_error
    - apisec_inspection_content_update_check
    - apisec_inspection_issue_creation_error_count
    - apisec_inspection_lru_error
    - apisec_inspection_message_processing_duration_bucket
    - apisec_inspection_message_processing_duration_count
    - apisec_inspection_message_processing_duration_sum
    - apisec_inspection_pubsub_input_counter
    - apisec_inspection_pubsub_input_error_counter
    - apisec_inspection_pubsub_output_error_counter
    - apisec_inspection_rule_error
    - apisec_inspection_rule_evaluation_duration_bucket
    - apisec_inspection_rule_execution_duration_bucket
    - apisec_inspection_telemetry_error_counter
    - apisec_risk_engine_drift_detection_duration_seconds_bucket
    - apisec_risk_engine_dspm_classification_call_duration_in_seconds_bucket
    - apisec_risk_engine_findings_upsert_duration_seconds_bucket
    - apisec_risk_engine_findings_upsert_duration_seconds_count
    - apisec_risk_engine_findings_upsert_duration_seconds_sum
    - apisec_risk_engine_get_speclets_duration_seconds_bucket
    - apisec_risk_engine_get_spec_duration_seconds_bucket
    - apisec_risk_engine_issues_upsert_duration_seconds_bucket
    - apisec_risk_engine_issues_upsert_duration_seconds_count
    - apisec_risk_engine_issues_upsert_duration_seconds_sum
    - apisec_risk_engine_transaction_handling_duration_in_seconds_bucket
    - apisec_risk_engine_transaction_handling_duration_in_seconds_count
    - apisec_risk_engine_transaction_handling_duration_in_seconds_sum
    - apisec_risk_engine_transactions_risk_detection_duration_in_seconds_bucket
    - apisec_risk_engine_transactions_risk_detection_duration_in_seconds_count
    - apisec_risk_engine_transactions_risk_detection_duration_in_seconds_sum
    - apisec_spec_service_cron_spec_gate_duration_seconds_bucket
    - apisec_spec_service_file_size_bytes_bucket
    - apisec_spec_service_errors_total
    - apisec_spec_service_mongo_duration_seconds_bucket
    - apisec_spec_service_pubsub_duration_seconds_bucket
    - itdr_risk_processor_risk_cron_duration_seconds_sum
    - itdr_risk_processor_risk_cron_duration_seconds_count
    - itdr_risk_processor_risk_cron_duration_seconds_seconds_bucket
    - itdr_risk_processor_dao_access_duration_seconds_sum
    - itdr_risk_processor_dao_access_duration_seconds_count
    - itdr_risk_processor_dao_access_duration_seconds_bucket
    - itdr_risk_processor_pubsub_input_error_counter
    - itdr_risk_processor_pubsub_output_error_counter
    - itdr_data_pipeline_asset_enrichers_duration_seconds_bucket
    - itdr_data_pipeline_asset_enrichers_duration_seconds_count
    - itdr_data_pipeline_asset_enrichers_duration_seconds_sum
    - itdr_data_pipeline_assetless_finding_syncer_duration_seconds_bucket
    - itdr_data_pipeline_assetless_finding_syncer_duration_seconds_count
    - itdr_data_pipeline_assetless_finding_syncer_duration_seconds_sum
    - itdr_data_pipeline_single_enricher_duration_seconds_bucket
    - itdr_data_pipeline_single_enricher_duration_seconds_count
    - itdr_data_pipeline_single_enricher_duration_seconds_sum
    - itdr_data_pipeline_cdc_syncer_duration_seconds_bucket
    - itdr_data_pipeline_cdc_syncer_duration_seconds_count
    - itdr_data_pipeline_cdc_syncer_duration_seconds_sum
    - itdr_data_pipeline_assets_relations_duration_seconds_bucket
    - itdr_data_pipeline_assets_relations_duration_seconds_count
    - itdr_data_pipeline_assets_relations_duration_seconds_sum
    - itdr_data_pipeline_pwd_analyzer_process_duration_seconds_bucket
    - itdr_data_pipeline_pwd_analyzer_process_duration_seconds_count
    - itdr_data_pipeline_pwd_analyzer_process_duration_seconds_sum
    - itdr_data_pipeline_assets_retention_duration_seconds_bucket
    - itdr_data_pipeline_assets_retention_duration_seconds_count
    - itdr_data_pipeline_assets_retention_duration_seconds_sum
    - itdr_data_pipeline_dss_sync_input_error_count
    - itdr_data_pipeline_dss_sync_input_error_count_total
    - itdr_data_pipeline_cie_row_errors_processed
    - itdr_data_pipeline_cie_row_errors_processed_total
    - itdr_data_pipeline_asset_metadata_error_count
    - itdr_data_pipeline_asset_metadata_error_count_total
    - itdr_data_pipeline_cdc_error_count
    - itdr_data_pipeline_cdc_error_count_total
    - itdr_data_pipeline_risk_handler_error_count
    - itdr_data_pipeline_cie_total_rows_process_duration_seconds_sum
    - itdr_data_pipeline_cie_total_rows_process_duration_seconds_count
    - itdr_data_pipeline_cie_total_rows_process_duration_seconds_bucket
    - itdr_data_pipeline_cie_rows_processed
    - itdr_data_pipeline_cie_rows_processed_total
    - itdr_data_pipeline_deleted_assets_total_rows_process_duration_sum
    - itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_sum
    - itdr_data_pipeline_deleted_assets_total_rows_process_duration_count
    - itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_count
    - itdr_data_pipeline_deleted_assets_total_rows_process_duration_bucket
    - itdr_data_pipeline_deleted_assets_total_rows_process_duration_seconds_bucket
    - itdr_data_pipeline_asset_metadata_read_duration_seconds_sum
    - itdr_data_pipeline_asset_metadata_read_duration_seconds_count
    - itdr_data_pipeline_asset_metadata_read_duration_seconds_bucket
    - itdr_data_pipeline_asset_metadata_write_duration_seconds_sum
    - itdr_data_pipeline_asset_metadata_write_duration_seconds_count
    - itdr_data_pipeline_asset_metadata_write_duration_seconds_bucket
    - itdr_data_pipeline_cdc_write_duration_seconds_sum
    - itdr_data_pipeline_cdc_write_duration_seconds_count
    - itdr_data_pipeline_cdc_write_duration_seconds_bucket
    - itdr_data_pipeline_risk_handler_read_duration_seconds_sum
    - itdr_data_pipeline_risk_handler_read_duration_seconds_count
    - itdr_data_pipeline_risk_handler_read_duration_seconds_bucket
    - itdr_data_pipeline_asset_updates_count_total
    - itdr_data_pipeline_asset_updates_duration_seconds_bucket
    - itdr_data_pipeline_asset_updates_duration_seconds_count
    - itdr_data_pipeline_asset_updates_duration_seconds_sum
    - itdr_data_pipeline_asset_updates_error_count_total
    - itdr_data_pipeline_asset_updates_manager_input_count_total
    - itdr_data_pipeline_asset_updates_manager_input_error_count_total
    - itdr_data_pipeline_dp_bus_error_count_total
    - itdr_data_pipeline_ad_hygiene_input_count_total
    - itdr_data_pipeline_ad_hygiene_input_error_count_total
    - itdr_data_pipeline_ad_hygiene_dtos_processed_total
    - itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_bucket
    - itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_count
    - itdr_data_pipeline_ad_hygiene_total_process_duration_seconds_sum
    - itdr_data_pipeline_baseline_metadata_error_counter_total
    - itdr_data_pipeline_realtime_updater_duration_seconds_bucket
    - itdr_data_pipeline_realtime_updater_duration_seconds_count
    - itdr_data_pipeline_realtime_updater_duration_seconds_sum
    - itdr_data_pipeline_cap_data_upload_duration_seconds_bucket
    - itdr_data_pipeline_cap_data_upload_duration_seconds_count
    - itdr_data_pipeline_cap_data_upload_duration_seconds_sum
    - itdr_data_pipeline_cap_data_repository_duration_seconds_bucket
    - itdr_data_pipeline_cap_data_repository_duration_seconds_count
    - itdr_data_pipeline_cap_data_repository_duration_seconds_sum
    - itdr_data_pipeline_baseline_total_rows_process_duration_seconds_bucket
    - itdr_data_pipeline_baseline_total_rows_process_duration_seconds_count
    - itdr_data_pipeline_baseline_total_rows_process_duration_seconds_sum
    - apiserver_client_certificate_expiration_seconds_bucket
    - apiserver_storage_data_key_generation_latencies_microseconds_bucket
    - app_hub_prometheus_ingester_edr_errors_total
    - app_hub_prometheus_ingester_xql_errors_total
    - application_hub_permanent_errors_total
    - application_hub_public_api_requests_total
    - application_hub_temporary_errors_total
    - archive_storage_aggregator_aggregator_compression_rate
    - archive_storage_aggregator_aggregator_process_duration
    - archive_storage_aggregator_committed_object_size
    - archive_storage_aggregator_compression_job_status
    - archive_storage_aggregator_delete_objects_count
    - archive_storage_aggregator_parse_error_count
    - archive_storage_aggregator_process_object_duration_micro_seconds_bucket
    - archive_storage_aggregator_processed_bytes_total
    - archive_storage_aggregator_processed_objects_count
    - archive_storage_aggregator_raw_object_size
    - asm_alerts_mitre_backfill_sync_error_total
    - asm_export_assets_gauge
    - asm_incidents_mitre_backfill_sync_error_total
    - asm_mitre_mappings_sync_error_total
    - asset_mgmt_assoc_engine_acquire_lock_count_total
    - asset_mgmt_assoc_engine_association_conflicts_count_total
    - asset_mgmt_assoc_engine_association_process_time_count
    - asset_mgmt_assoc_engine_association_process_time_sum
    - asset_mgmt_diff_maker_last_exec_time_sec
    - asset_mgmt_general_assets_count_per_cloud_provider
    - asset_mgmt_general_assets_count_per_source
    - asset_mgmt_general_total_assets_count
    - asset_mgmt_ingester_assets_processed_count_total
    - asset_mgmt_reducer_last_exec_time_sec
    - asset_mgmt_snapshot_mgr_acquire_lock_total
    - attack_path_failure
    - attack_path_rules
    - attack_path_start_total
    - attack_path_success_total
    - attack_path_verdicts
    - auto_suggest_failure_total
    - batch_scanner_assets
    - batch_scanner_assets_scanned_milliseconds_bucket
    - batch_scanner_rules_processed_total
    - batch_scanner_verdict_generated
    - blast_radius_crown_jewel_counter_total
    - blast_radius_path_counter_total
    - blast_radius_paths_by_length_total
    - blast_radius_service_time_seconds
    - blast_radius_source_counter_total
    - cas_applications_job_application_functions_seconds_bucket
    - cas_applications_job_application_functions_seconds_count
    - cas_applications_job_application_functions_seconds_sum
    - cas_dashboards_api_service_duration_seconds_bucket
    - cas_dashboards_api_service_duration_seconds_count
    - cas_dashboards_api_service_duration_seconds_sum
    - cas_product_analytics_send_to_cortex_seconds_bucket
    - cas_product_analytics_send_to_cortex_seconds_count
    - cas_product_analytics_send_to_cortex_seconds_sum
    - cas_persistence_app_code_errors_total
    - cas_persistence_app_genesis_errors_total
    - cas_persistence_app_lens_errors_total
    - cas_persistence_app_stream_errors_total
    - cas_persistence_core_errors_total
    - cas_persistence_flow_errors_total
    - cas_persistence_issues_errors_total
    - cas_persistence_lilo_errors_total
    - cas_persistence_scai_errors_total
    - cas_persistence_scan_ops_errors_total
    - cas_persistence_stitch_errors_total
    - cas_source_control_source_control_pr_scan_until_status_duration_seconds_count
    - cas_source_control_source_control_pr_scan_until_status_duration_seconds_sum
    - cas_source_control_source_control_pr_scan_until_status_duration_seconds_bucket
    - cas_unified_cli_command_count_total
    - cas_unified_cli_os_total
    - cas_unified_cli_scan_result_total
    - cc_cache_update_key_failure_total
    - cc_cache_update_time_seconds_bucket
    - cc_cache_update_time_seconds_count
    - cc_cache_update_time_seconds_sum
    - ciem_gauge_epc_snapshot_status_account_monitor
    - ciem_gauge_epc_status_account_monitor
    - ciem_counter_epc_sync_page_upload_failure_counter_total
    - ciem_counter_epc_validation_snapshot_failure_counter_total
    - ciem_gauge_epc_sync_permissions_calculated_gauge
    - ciem_gauge_metrics_publisher_total_issues
    - ciem_timer_account_organization_syncer_get_configs_time_seconds_max
    - ciem_timer_cortex_account_organization_syncer_calculate_epc_time_seconds_max
    - ciem_timer_epc_consumer_total_time_seconds_max
    - ciem_timer_epc_sync_total_sync_time_seconds_count
    - ciem_timer_epc_phase_total_time_seconds_max
    - ciem_timer_epc_phase_total_time_seconds_count
    - ciem_timer_epc_phase_total_time_seconds_sum
    - ciem_timer_rule_scanner_orchestrator_run_seconds_count
    - ciem_timer_rule_scanner_orchestrator_run_seconds_max
    - ciem_timer_graph_controller_calculate_graph_seconds_count
    - ciem_timer_graph_controller_calculate_graph_seconds_sum
    - ciem_timer_table_controller_get_data_seconds_count
    - ciem_timer_table_controller_get_data_seconds_sum
    - ciem_timer_table_controller_get_view_def_seconds_count
    - ciem_timer_table_controller_get_view_def_seconds_sum
    - ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_count
    - ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_sum
    - ciem_timer_least_privileged_access_controller_metadata_seconds_count
    - ciem_timer_least_privileged_access_controller_metadata_seconds_sum
    - ciem_timer_least_privileged_access_controller_optimize_seconds_count
    - ciem_timer_least_privileged_access_controller_optimize_seconds_sum
    - classification_mgmt_bigquery_write_error_total
    - classification_mgmt_content_delivery_error_total
    - classification_mgmt_dashboard_error_total
    - classification_mgmt_pubsub_publish_error_total
    - cloud_assets_collection_csp_api_request_duration_seconds_bucket
    - cloud_assets_collection_csp_api_request_duration_seconds_count
    - cloud_assets_collection_csp_api_request_duration_seconds_sum
    - cloud_assets_collection_message_processing_duration_seconds_bucket
    - cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket
    - cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count
    - cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum
    - cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket
    - cloud_assets_collection_num_failed_tasks_total
    - cloud_assets_collection_num_successful_tasks_total
    - cloud_assets_collection_platform_api_request_duration_seconds_bucket
    - cloud_connectors_templates_created_total
    - cloud_onboarding_errors_total
    - cloud_outposts_templates_created_total
    - cns_asset_conversion_errors_total
    - cns_invalid_policies
    - cns_invalid_policies
    - cns_invalid_rules
    - cns_issues_emitter_duration_seconds_count
    - cns_job_duration_seconds_sum
    - container_blkio_device_usage_total
    - cold_storage_datasets_aggregator_aggregator_compression_rate
    - cold_storage_datasets_aggregator_aggregator_process_duration
    - cold_storage_datasets_aggregator_committed_object_size
    - cold_storage_datasets_aggregator_compression_job_status
    - cold_storage_datasets_aggregator_dataset_errors_count
    - cold_storage_datasets_aggregator_delete_objects_count
    - cold_storage_datasets_aggregator_processed_bytes_total
    - cold_storage_datasets_aggregator_processed_objects_count
    - cold_storage_datasets_aggregator_raw_object_size
    - cold_storage_datasets_aggregator_spawned_aggregators_count
    - container_cpu_cfs_periods_total
    - container_cpu_cfs_throttled_periods_total
    - container_cpu_cfs_throttled_seconds_total
    - container_cpu_load_average_10s
    - container_cpu_schedstat_run_periods_total
    - container_cpu_schedstat_run_seconds_total
    - container_cpu_schedstat_runqueue_seconds_total
    - container_cpu_system_seconds_total
    - container_cpu_usage_seconds_total
    - container_cpu_user_seconds_total
    - container_fs_inodes_free
    - container_fs_inodes_total
    - container_fs_io_current
    - container_fs_io_time_seconds_total
    - container_fs_io_time_weighted_seconds_total
    - container_fs_limit_bytes
    - container_fs_read_seconds_total
    - container_fs_reads_bytes_total
    - container_fs_reads_merged_total
    - container_fs_reads_total
    - container_fs_sector_reads_total
    - container_fs_sector_writes_total
    - container_fs_usage_bytes
    - container_fs_write_seconds_total
    - container_fs_writes_bytes_total
    - container_fs_writes_merged_total
    - container_fs_writes_total
    - container_last_seen
    - container_memory_cache
    - container_memory_failcnt
    - container_memory_failures_total
    - container_memory_mapped_file
    - container_memory_max_usage_bytes
    - container_memory_rss
    - container_memory_swap
    - container_memory_usage_bytes
    - container_memory_working_set_bytes
    - container_network_receive_packets_dropped_total
    - container_network_receive_packets_total
    - container_network_tcp_usage_total
    - container_network_transmit_bytes_total
    - container_network_udp_usage_total
    - container_processes
    - container_sockets
    - container_spec_cpu_period
    - container_spec_cpu_quota
    - container_spec_cpu_shares
    - container_spec_memory_limit_bytes
    - container_spec_memory_reservation_limit_bytes
    - container_spec_memory_swap_limit_bytes
    - container_start_time_seconds
    - container_tasks_state
    - container_ulimits_soft
    - contextual_search_graph_neo4j_query_execution_time_millis_bucket
    - contextual_search_graph_neo4j_query_execution_time_millis_count
    - contextual_search_graph_neo4j_query_execution_time_millis_created
    - contextual_search_graph_neo4j_query_execution_time_millis_sum
    - cronus_client_client_roundtrip_latency_sec_bucket
    - cronus_client_connection_wait_duration_seconds_bucket
    - cronus_client_rate_limited_requests_total
    - cronus_client_requests_total
    - cronus_hotkeys_count_delay_duration_seconds_bucket
    - cronus_hotkeys_count_delay_duration_seconds_count
    - cronus_hotkeys_count_delay_duration_seconds_sum
    - cronus_hotkeys_count_duration_seconds_bucket
    - cronus_hotkeys_count_duration_seconds_count
    - cronus_hotkeys_count_duration_seconds_sum
    - cronus_db_repair_dropped_entries_total
    - cronus_last_processed_index
    - cronus_rebalance_download_bytes_total
    - cronus_rebalance_read_bytes_total
    - cronus_rebalance_upload_bytes_total
    - cronus_rows_index_latency_seconds_bucket
    - cronus_throttled_write_requests_total
    - custom_rules_counter_total
    - cwp_dangling_resources_total
    - cronus_tree_index_compaction_duration_seconds_bucket
    - cwp_api_concurrent_requests
    - cwp_api_latency_seconds_bucket
    - cwp_api_latency_seconds_count
    - cwp_api_latency_seconds_sum
    - cwp_api_requests_total
    - cwp_malwaredetection_wildfire_latency_seconds_bucket
    - cwp_malwaredetection_wildfire_latency_seconds_count
    - cwp_malwaredetection_wildfire_latency_seconds_sum
    - cwp_sp_snapshot_auto_deleted_total
    - cwp_sp_snapshot_csp_requests_total
    - cwp_sp_snapshot_dangling_total
    - cwp_sp_snapshot_lifetime_seconds_bucket
    - cwp_sp_snapshot_operation_duration_seconds_bucket
    - cwp_sp_snapshot_requests_missed_sla
    - dashboard_api_4xx_failure_total
    - dashboard_api_5xx_failure_total
    - dashboard_engine_dashboard_engine_widgets_access_created
    - dashboard_engine_mv_creation_failure_created
    - dashboard_engine_mv_creation_success_created
    - dashboard_engine_mv_status_worker_failures_created
    - dbre_backup_succeeded
    - st_nginx_request_status_code_total
    - st_nginx_http_request_duration_seconds_sum
    - st_nginx_http_request_duration_seconds_count
    - dms_controller_dms_query_time_count
    - dms_controller_dms_query_time_sum
    - dms_controller_dropped_stories_total
    - dms_controller_element_duration_seconds
    - dms_controller_element_duration_seconds_count
    - dms_controller_element_duration_seconds_sum
    - dms_controller_element_status
    - dms_controller_integrator_events
    - dms_dedup_ingestion_duration_count
    - dms_dedup_num_ingested_acked
    - dms_dedup_num_sunmitted
    - dp_asset_pipeline_assets_total
    - dp_asset_pipeline_assets_errors
    - dp_asset_associations_pipeline_assets_batch_size_bucket
    - dp_asset_associations_pipeline_association_big_query_result_size_bucket
    - dp_asset_pipeline_metablob_errors
    - dp_asset_pipeline_performance_count
    - dp_asset_pipeline_performance_sum
    - dp_asset_pipeline_performance_bucket
    - dp_asset_pipeline_rows_in_metablobs_bucket
    - dp_asset_pipeline_skip_get_deleted_assets
    - dp_asset_pipeline_skip_get_deleted_assets_counter
    - dp_asset_pipeline_operation_total
    - dspm_dt_bigquery_job_affected_rows_total
    - dspm_dt_bigquery_job_duration_seconds_count
    - dspm_dt_bigquery_job_duration_seconds_max
    - dspm_dt_bigquery_job_duration_seconds_sum 
    - dspm_dt_bigquery_job_processed_bytes_total
    - dspm_dt_bigquery_job_processed_partitions_total
    - dspm_dt_bigquery_job_slot_millis_total
    - dspm_dt_mac_assets_publish_total
    - dspm_dt_mac_findings_publish_total
    - dspm_dt_mac_query_execution_duration_seconds_count
    - dspm_dt_mac_query_execution_duration_seconds_sum
    - dspm_dt_mac_query_execution_success_count_total
    - dspm_dt_mac_query_execution_failure_count_total
    - effective_ip_range_monitoring_overlapping_rules
    - edr_controller:rate:1d
    - edr_controller_element_duration_seconds
    - edr_controller_element_duration_seconds_count
    - edr_controller_element_duration_seconds_sum
    - egress_aggregator_aggregator_compression_rate
    - egress_aggregator_committed_object_size
    - egress_aggregator_compression_job_status
    - egress_aggregator_delete_objects_count
    - egress_aggregator_processed_bytes_total
    - egress_aggregator_processed_objects_count
    - egress_aggregator_raw_object_size
    - egress_aggregator_spawned_aggregators_count
    - email_relay_pubsub_total
    - email_relay_scheduler_task_executions_total
    - findings_table_fetching_time_seconds_bucket
    - findings_table_fetching_time_seconds_created
    - finding_operation_total
    - finding_metablob_performance_count
    - finding_metablob_performance_sum
    - finding_metablob_performance_bucket
    - findings_table_fetching_time_seconds_sum
    - flux_resource_info
    - xdr_fas_request_total
    - GnzGlobal_DAO_operation_latency_seconds_bucket
    - GnzGlobal_DynamicConfig_get_config_failures_total
    - GnzGlobal_json_stream_bigquery_written_bytes
    - GnzGlobal_pithos_active_streams
    - GnzGlobal_pithos_aggregated_bytes_total
    - GnzGlobal_pithos_aggregated_objects_total
    - GnzGlobal_pithos_aggregation_duration_seconds
    - GnzGlobal_pithos_client_stream_commit_latency_seconds_count
    - GnzGlobal_pithos_client_stream_commit_latency_seconds_sum
    - GnzGlobal_pithos_client_stream_init_latency_seconds_bucket
    - GnzGlobal_pithos_client_send_on_stream_latency_seconds_bucket
    - GnzGlobal_pithos_client_stream_commit_latency_seconds_bucket
    - GnzGlobal_pithos_committed_objects_total
    - GnzGlobal_pithos_dataset_aggregators
    - GnzGlobal_pithos_streamed_bytes_total
    - GnzGlobal_PipelineMonitoring_full_message_process_duration_seconds_bucket
    - GnzGlobal_PipelineMonitoring_single_row_process_duration_seconds_bucket
    - GnzGlobal_storage_write_api_append_rows_duration_ms_count
    - GnzGlobal_storage_write_api_append_rows_duration_ms_sum
    - GnzGlobal_storage_write_api_number_of_lines_written
    - GnzGlobal_storage_write_api_status_total
    - GnzGlobal_Quantum_last_insert_time_seconds
    - GnzStoryBuilder_PartitionConsumer_last_processed_index
    - http_server_duration_milliseconds_bucket
    - http_server_duration_milliseconds_count
    - http_server_duration_milliseconds_sum
    - http_server_request_duration_seconds_bucket
    - http_server_request_duration_seconds_count
    - http_server_request_duration_seconds_sum
    - http_server_response_size_bytes_sum
    - http_server_response_size_bytes_count
    - hydra_edr_upload_bucket_duration_seconds_count
    - hydra_edr_upload_bucket_duration_seconds_sum
    - hydra_edr_written_bucket_bytes
    - hydra_invalid_token
    - hydra_mt_req_path_counter
    - hydra_mt_req_path_payload_mb
    - hydra_mt_res_path_payload_mb
    - hydra_none_responsive_tenants
    - hydra_pb_latency_count
    - hydra_pb_latency_sum
    - hydra_req_path_counter
    - hydra_req_path_payload_mb
    - hydra_res_path_payload_mb
    - hydra_server_con_count
    - hydra_server_http_status_code
    - hydra_server_request_latency_second_count
    - hydra_server_request_latency_second_sum
    - hydra_ws_active_conn
    - hydra_ws_conn_duration_seconds_count
    - hydra_ws_conn_duration_seconds_sum
    - itdr_data_pipeline_assets_retention_duration_seconds_bucket
    - itdr_data_pipeline_assets_retention_duration_seconds_count
    - itdr_data_pipeline_assets_retention_duration_seconds_sum
    - itdr_data_pipeline_baseline_metadata_error_counter
    - itdr_data_pipeline_baseline_total_rows_process_duration_seconds_bucket
    - itdr_data_pipeline_baseline_total_rows_process_duration_seconds_count
    - itdr_data_pipeline_baseline_total_rows_process_duration_seconds_sum
    - itdr_data_pipeline_cie_enricher_duration_bucket
    - itdr_data_pipeline_cie_enricher_duration_count
    - itdr_data_pipeline_cie_enricher_duration_sum
    - itdr_data_pipeline_cie_enricher_error_count
    - itdr_data_pipeline_password_analyzer_input_count
    - itdr_data_pipeline_password_analyzer_input_error_count
    - itdr_data_pipeline_pwd_analyzer_dao_read_error
    - itdr_data_pipeline_pwd_analyzer_handle_close_findings_error
    - itdr_data_pipeline_pwd_analyzer_handle_open_findings_error
    - itdr_data_pipeline_pwd_analyzer_matches
    - itdr_data_pipeline_pwd_analyzer_password_analyzer_error
    - itdr_data_pipeline_pwd_analyzer_process_duration_seconds_bucket
    - itdr_data_pipeline_pwd_analyzer_process_duration_seconds_count
    - itdr_data_pipeline_pwd_analyzer_process_duration_seconds_sum
    - itdr_data_pipeline_risk_updater_duration_seconds_bucket
    - itdr_data_pipeline_risk_updater_duration_seconds_count
    - itdr_data_pipeline_risk_updater_duration_seconds_sum
    - kube_configmap_created
    - kube_configmap_info
    - kube_deployment_status_condition
    - kube_horizontalpodautoscaler_status_condition
    - kube_namespace_created
    - kube_pod_annotations
    - kube_pod_completion_time
    - kube_pod_container_resource_limits
    - kube_pod_container_resource_limits_cpu_cores
    - kube_pod_container_resource_limits_memory_bytes
    - kube_pod_container_resource_requests
    - kube_pod_created
    - kube_pod_info
    - kube_pod_labels
    - kube_pod_owner
    - kube_pod_restart_policy
    - kube_pod_spec_volumes_persistentvolumeclaims_info
    - kube_pod_spec_volumes_persistentvolumeclaims_readonly
    - kube_pod_start_time
    - kube_pod_status_phase
    - kube_pod_status_ready
    - kube_pod_status_reason
    - kube_pod_status_scheduled
    - kube_pod_status_scheduled_time
    - kube_replicaset_created
    - kube_resourcequota
    - kubelet_container_log_filesystem_used_bytes
    - kubelet_docker_operations_latency_microseconds
    - kubelet_runtime_operations
    - kubelet_runtime_operations_latency_microseconds
    - kubelet_runtime_operations_latency_microseconds_count
    - kubelet_runtime_operations_latency_microseconds_sum
    - lavawall_controller:rate:1d
    - migration_time_consumed
    - node_scrape_collector_duration_seconds
    - node_scrape_collector_success
    - node_systemd_unit_state
    - number_of_cloud_accounts
    - netscan_avg_target_size
    - netscan_kms_error_total
    - netscan_processor_cns_host_prior_scan_age_bucket
    - netscan_processor_dao_access_duration_seconds_bucket
    - netscan_processor_netscan_results_batch_process_bucket
    - netscan_processor_scan_result_batch_size_bucket
    - netscan_processor_scan_result_incompatible
    - netscan_processor_scan_result_match
    - netscan_processor_scan_result_message_count_bucket
    - netscan_processor_scan_result_with
    - netscan_processor_scan_task_processed
    - netscan_processor_uai_pubsub_output_error
    - netscan_scans_configured
    - netscan_scans_scheduled
    - partyzaurus_controller:rate:1d
    - partyzaurus_controller_element_duration_seconds
    - partyzaurus_controller_element_duration_seconds_count
    - partyzaurus_controller_element_duration_seconds_sum
    - process_cpu_seconds_total
    - process_open_fds
    - process_resident_memory_bytes
    - process_start_time_seconds
    - process_virtual_memory_bytes
    - python_gc_collected_objects_bucket
    - python_gc_duration_seconds_bucket
    - python_gc_uncollectable_objects_bucket
    - pz_schema_manager_pz_api_call_counter
    - pz_schema_manager_pz_api_call_duration_seconds_count
    - pz_schema_manager_pz_api_call_duration_seconds_sum
    - risk_prioritization_action_plan_counter_total
    - risk_prioritization_action_plan_score_gauge_ratio
    - risk_prioritization_issue_score_gauge_ratio
    - risk_prioritization_learn_state_check_ratio
    - risk_prioritization_model_bq_import_seconds
    - risk_prioritization_model_create_date_gauge_seconds
    - risk_prioritization_upload_model_seconds
    - rule_migration_errors_total
    - rule_migration_result
    - scylla_reactor_utilization
    - scylla_storage_proxy_coordinator_read_latency_bucket
    - scylla_storage_proxy_coordinator_write_latency_bucket
    - spur_dms_actual_batch_size_bucket
    - spur_dms_actual_batch_size_count
    - spur_dms_actual_batch_size_sum
    - spur_dms_cache_size
    - spur_dms_fetch_duration_seconds_bucket
    - spur_dms_fetch_duration_seconds_count
    - spur_dms_fetch_duration_seconds_sum
    - spur_dms_fetch_errors_count
    - spur_dms_inactive_until_unix_milli
    - spur_dms_ip_parse_error
    - spur_dms_poll_failure_count
    - spur_dms_queried_count
    - spur_dms_request_count
    - spur_dms_skipped_ips_count
    - spur_dms_spur_generation_time_unix_milli
    - spur_pz_actual_batch_size_bucket
    - spur_pz_actual_batch_size_count
    - spur_pz_actual_batch_size_sum
    - spur_pz_cache_size
    - spur_pz_fetch_duration_seconds_bucket
    - spur_pz_fetch_duration_seconds_count
    - spur_pz_fetch_duration_seconds_sum
    - spur_pz_fetch_errors_count
    - spur_pz_inactive_until_unix_milli
    - spur_pz_ip_parse_error
    - spur_pz_poll_failure_count
    - spur_pz_queried_count
    - spur_pz_request_count
    - spur_pz_skipped_ips_count
    - standard_events_event_dropped_counter_created
    - standard_events_event_dropped_counter_total
    - standard_events_event_failed_sys_error_counter_created
    - standard_events_event_failed_sys_error_counter_total
    - standard_events_event_failed_validation_counter_created
    - standard_events_event_failed_validation_counter_total
    - standard_events_event_processed_success_counter_created
    - standard_events_event_processed_success_counter_total
    - standard_events_queue_size
    - spur_pz_spur_generation_time_unix_milli
    - storage_operation_status_count
    - storybuilder_dwp_failed_updates_created
    - temporal_worker_task_slots_available
    - temporal_worker_task_slots_used
    - tenant:analytics_product_last_successful_api_table_creation_timestamp:max
    - tenant_log_bytes_by_application
    - tenant_log_count_by_application
    - uai_aggregated_assets_cdc_delay_seconds
    - xcloud_gcp_snapshot_collection_count
    - xdm_dirty_level_increased_created
    - xdm_view_refresh_count_created
    - xdr_agent_api_content_bw_alloc_success_created
    - xdr_agent_api_content_bw_optimization_acquisition_usage_created
    - xdr_agent_api_content_bw_optimization_insufficient_created
    - xdr_agent_config_recalc_task_timeouts_count_created
    - xdr_agent_configuration_errors_created
    - xdr_agent_configuration_fetching_error_created
    - xdr_agent_configuration_recalculate_counter_created
    - xdr_agent_configuration_recalculate_time_created
    - xdr_agent_configuration_single_recalculate_error_created
    - xdr_agent_configuration_single_recalculate_summary_created
    - xdr_agent_heartbeat_response_empty_created
    - xdr_agent_heartbeat_response_time_created
    - xdr_agent_modification_calls_count_created
    - xdr_agent_reports_parse_periodic_error_created
    - xdr_agent_reports_parse_prevention_error_created
    - xdr_agents_failed_registrations_created
    - xdr_alert_source_delay_time
    - xdr_alert_sync_tags_databases_error_total
    - xdr_alerts_fetcher_new_ingest_total
    - xdr_alerts_fetcher_parse_error_total
    - xdr_alerts_excluded_count_created
    - xdr_alerts_fetcher_deduplicate_error_counter_created
    - xdr_alerts_fetcher_deduplicated_counter_created
    - xdr_alerts_fetcher_fail_ingest_created
    - xdr_alerts_matched_to_layout_rules_count_created
    - xdr_alerts_matcher_load_time_created
    - xdr_alerts_not_matched_to_layout_rules_created
    - xdr_alerts_query_xsoar_count_created
    - xdr_alerts_table_query_count_created
    - xdr_alerts_table_update_count_created
    - xdr_alerts_view_get_alerts_bq_error_total
    - xdr_asset_command_collector_installations_count_created
    - xdr_calc_edr_alert_simulation_retention_info_days_created
    - xdr_calc_last_association_replication_time
    - xdr_card_bottom_tables_counter_created
    - xdr_card_bottom_tables_data_fetched_counter_created
    - xdr_card_get_children_counter_created
    - xdr_card_get_parent_counter_created
    - xdr_card_opened_counter_created
    - xdr_card_tree_created_counter_created
    - xdr_card_tree_created_summary_created
    - xdr_case_table_loading_time_created
    - xdr_case_to_tags_recalculation_created
    - xdr_case_to_tags_specific_case_calc_created
    - xdr_case_view_missing_metadata_created
    - xdr_collection_number_of_events_per_collection_created
    - xdr_cts_active_tokens
    - xdr_cts_active_sessions
    - xdr_clcs_multi_csp_connected
    - xdr_clcs_multi_region_connected_count
    - xdr_clcs_multi_region_enabled
    - xdr_clcs_multi_salesforce_enabled
    - xdr_dss_request_duration_created
    - xdr_dss_response_code_created
    - xdr_dss_response_size_created
    - xdr_egress_oldest_raw_object
    - xdr_email_to_issue_latency_bucket
    - xdr_email_to_issue_latency_count
    - xdr_email_to_issue_latency_sum
    - xdr_gvs_upload_session_created_created
    - xdr_logging_error_created
    - xdr_mysql_connection_errors_created
    - xdr_mysql_connection_status_created
    - xdr_mysql_innodb_pages_created
    - xdr_mysql_threads_created
    - xdr_preprocessed_data_batcher_oldest_object
    - xdr_redis_errors_counter_created
    - xdr_reports_generator_handle_error_created
    - xdr_reports_generator_message_count_created
    - xdr_reports_generator_process_message_time_created
    - xdr_search_index_creation_errors_created
    - xdr_unknown_email_issue_type_total
    - xdr_vulnerability_assessment_last_recalculate_task_timestamp_created
    - xdr_vulnerability_assessment_recalculate_task_duration_time_created
    - xdr_wlm_task_delay_created
    - xpanse_alert_fetcher_missing_policy_created
    - xpanse_alert_fetcher_reopened_alert_created
    - xpanse_alert_fetcher_resolved_alert_created
    - xpanse_global_lookup_request_time_count
    - xpanse_global_lookup_request_time_sum
    - xpanse_global_lookup_request_errors_total
    - xpanse_widget_errors_created
    - xpanse_widget_interactions_created
    - xpanse_widget_runtime_summary_created
    - xql_custom_widgets_created_counter_created
    - xql_email_errors_total
    - xql_engine_dataset_columns_count
    - xql_engine_output_counter
    - xql_engine_send_detect_counter
    - xql_logs_iterator_parser_panics_total
    - xql_non_xdr_queries_counter_created
    - xql_raw_schema_detect_errors_created
    - xdr_request_processing_seconds_count
    - xdr_request_processing_seconds_sum
    - xdr_wlm_task_started_created
    - xql_controller_element_duration_seconds
    - xql_controller_element_duration_seconds_count
    - xql_controller_element_duration_seconds_sum
    - .*_dedup_pend_.*_bucket
    - .*_grpc_.*
    - .*dms_dedup.*
    - .*edr_dedup.*
    - .*ext_dedup.*
    - ext_controller_element_duration_seconds
    - .*partyzaurus_dedup.*
    - .*replay_sos_dedup.*
    - .*rest_client.*duration_seconds.*
    - .*rest_client.*latency_seconds.*
    - .*xql_dedup.*
    - analytics_de_.*
    - analytics_detect.*
    - apiserver_.*
    - certwatcher.*
    - container_file.*
    - container_network.*
    - container_threads.*
    - controller_runtime_.*
    - edr_controller_element_status
    - failed_requests_total
    - fastxql.*
    - kube_node_status_condition
    - kube_pod_init_.*
    - kube_replicaset.*
    - kube_secret.*
    - kubelet.*duration_seconds.*
    - kubelet.*latency_seconds.*
    - kubelet_http.*
    - logback_events_total
    - memsql_info_.*
    - node_authorizer.*
    - oversized_alert_trimmed_field_bucket
    - partyzaurus_controller_element_status
    - partyzaurus_fusion_rate_limit
    - persistence_requests
    - python.*
    - rest_client_.*
    - scrape_.*
    - stackdriver_.*_compute_googleapis_com_instance_disk_.*
    - stackdriver_.*_logging_googleapis_com_.*
    - storage_operation_duration_seconds_.*
    - storybuilder_in_flight_story_timer_wait_duration_seconds_bucket
    - support_case_auto_generate_tsf_request_timeout_total
    - support_case_failed_auto_generate_tsf_request_total
    - support_case_failed_upload_files_total
    - support_case_number_of_support_case_failed_to_create_total
    - support_case_number_of_support_case_successfully_created_total
    - support_case_success_auto_generate_tsf_request_total
    - support_case_success_upload_files_total
    - mongo_polaris_inserts_total
    - uai_api_errors_count_total
    - uai_api_requests_count_total
    - vectorized_matcher_current_content_version_exporting_time_in_seconds
    - vectorized_matcher_current_content_version_loading_time_in_seconds
    - verdict_manager_batches_completed 
    - verdict_manager_verdicts_fetched 
    - verdict_manager_findings_created  
    - verdict_manager_findings_closed  
    - verdict_manager_findings_publish_failed 
    - verdict_manager_findings_publish_success 
    - verdict_manager_issues_created
    - verdict_manager_issues_updated 
    - verdict_manager_issues_closed 
    - verdict_manager_issues_batch_publish_success 
    - verdict_manager_issues_batch_publish_failed
    - verdict_manager_reconcile_failed
    - vsg_reconcile_time_bucket
    - vsg_oom_scaler_reconcile_time_bucket
    - vsg_oom_scaler_resolve_source_metric_bucket
    - vsg_vertical_scaler_reconcile_time_bucket
    - vsg_vertical_scaler_resolve_source_metric_bucket
    - vsg_zero_scaler_reconcile_time_bucket
    - vsg_zero_scaler_resolve_source_metric_bucket
    - vsg_pvc_scaler_reconcile_time_bucket
    - vsg_pvc_scaler_resolve_source_metric_bucket
    - vxp_http_server_requests_count
    - vxp_http_server_requests_sum
    - vxp_pubsub_processing_latency_bucket
    - workflow_failed
    - workflow_success
    - workflow_timeout
    - workqueue_.*
    - xdr_invalidate_user_role_failure
    - xdr_ios_large_digest_report_total
    - xdr_request_response_size_.*
    - xpanse_asset_tag_rules_error_total
    - xpanse_integrations_data_monitoring_metrics_prisma_collectors_enabled_counts
    - xpanse_integrations_data_monitoring_metrics_prisma_collectors_failed_counts
    - xpanse_integrations_data_monitoring_metrics_xcloud_collectors_enabled_counts
    - xsoar_auto_extract_enrich_all_indicators_bucket
    - xsoar_auto_extract_enrich_indicator_command_bucket
    - xsoar_auto_extract_entry_processing_bucket
    - xsoar_auto_extract_format_indicator_bucket
    - xsoar_auto_extract_find_indicator_bucket
    - xsoar_auto_extract_map_indicator_bucket
    - xsoar_msg_bus_dlq_subscriber_pull
    - xsoar_msg_bus_fetch_metrics_latency_bucket
    - xsoar_msg_bus_insert_latency_bucket
    - xsoar_msg_bus_lock_latency_bucket
    - xsoar_msg_bus_publisher
    - xsoar_msg_bus_subscriber_ack
    - xsoar_msg_bus_subscriber_ack_latency_bucket
    - xsoar_msg_bus_subscriber_nack
    - xsoar_msg_bus_subscriber_nack_latency_bucket
    - xsoar_msg_bus_subscriber_pull
    - xsoar_msg_bus_subscriber_pull_latency_bucket
    - xsoar_rc_counter_incidents_error
    - xsoar_rc_counter_incidents_linkedIncidents_error
    - xsoar_rc_counter_incidents_linkedIncidents_warning
    - xsoar_rc_counter_incidents_warning
    - xsoar_rc_counter_indicators_error
    - xsoar_rc_counter_indicators_investigations_error
    - xsoar_rc_counter_indicators_investigations_warning
    - xsoar_rc_counter_indicators_warning
    - xsoar_rc_counter_investigations_entries_error
    - xsoar_rc_counter_investigations_entries_warning
    - xsoar_rc_counter_investigations_indicatorsDefaultEntries_error
    - xsoar_rc_counter_investigations_indicatorsDefaultEntries_warning
    - xsoar_rc_counter_investigations_indicatorsDefaultEntriesJob_error
    - xsoar_rc_counter_investigations_indicatorsDefaultEntriesJob_warning
    - xsoar_rc_counter_investigations_indicatorsNonDefaultEntries_error
    - xsoar_rc_counter_investigations_indicatorsNonDefaultEntries_warning
    - xsoar_rc_counter_investigations_indicatorsNonDefaultEntriesJob_error
    - xsoar_rc_counter_investigations_indicatorsNonDefaultEntriesJob_warning
    - xsoar_rc_counter_TestObject_error
    - xsoar_rc_counter_TestObject_linkedIncidents_error
    - xsoar_rc_counter_TestObject_linkedIncidents_warning
    - xsoar_rc_counter_TestObject_warning
    - xsoar_rc_counter_with_time_range_incidents_error
    - xsoar_rc_counter_with_time_range_incidents_warning
    - xsoar_rc_counter_with_time_range_indicators_error
    - xsoar_rc_counter_with_time_range_indicators_warning
    - xsoar_rc_counter_with_time_range_moduleInstance_incidents_error
    - xsoar_rc_counter_with_time_range_moduleInstance_incidents_warning
    - xsoar_rc_counter_with_time_range_pyramidAPI_getAlerts_error
    - xsoar_rc_counter_with_time_range_pyramidAPI_getAlerts_warning
    - xsoar_rc_counter_with_time_range_TestObject_error
    - xsoar_rc_counter_with_time_range_TestObject_warning
    - xsoar_rc_size_attachments_error
    - xsoar_rc_size_attachments_warning
    - xsoar_rc_size_incidents_error
    - xsoar_rc_size_incidents_warning
    - xsoar_rc_size_indicators_error
    - xsoar_rc_size_indicators_warning
    - xsoar_rc_size_TestObject_error
    - xsoar_rc_size_TestObject_warning
    - xql_controller_element_status
    - xql_engine_input_counter
    - xql_fusion_ingested_events
    - xql_ingestion_log_bytes_.*
    - xql_ingestion_logs_count
    - xql_ingestion_raw_size_bytes
